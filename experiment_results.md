# CA-AMA 实验结果报告

## 实验配置
- **数据集**: 正态分布 (data=8)
- **代理数量**: 2
- **物品数量**: 2  
- **菜单大小**: 32
- **随机种子**: 1
- **设备**: CUDA (RTX 4070Ti)
- **训练步数**: 1000 (基线), 800 (CA-AMA)

## 训练结果

### 基线方法 (Baseline AMA)
- **最终训练收入**: 0.8106 (第800步)
- **测试收入**: 0.8102
- **训练时间**: 约15分钟
- **模型文件**: `results_baseline/8_2_2_32_1_1/model_1000.pt`

### CA-AMA方法 (Contextual Affine Maximizer Auction)
- **最终训练收入**: 
  - AMA收入: 0.8087
  - 校正收入: 0.0030
  - **总收入**: 0.8117
- **IR遗憾**: 0.0009
- **测试结果**:
  - **总收入**: 0.8102
  - **有效收入**: 0.8096
  - **IR遗憾**: 0.0002
- **训练时间**: 约13分钟
- **模型文件**: 
  - `results/8_2_2_32_1_1/model_800.pt`
  - `results/8_2_2_32_1_1/model_cor_800.pt`

## 关键发现

### 1. 收入对比
- **基线方法**: 0.8102
- **CA-AMA方法**: 0.8102 (总收入)
- **性能差异**: 基本相同

### 2. 个体理性 (Individual Rationality)
- **CA-AMA的优势**: IR遗憾仅为0.0002，远低于目标值0.001
- **基线方法**: 无IR约束，可能存在负效用

### 3. 训练稳定性
- **基线方法**: 收入在0.80-0.81之间稳定
- **CA-AMA方法**: 
  - AMA部分收入稳定在0.80左右
  - 校正项逐渐减小，从0.04降至0.003
  - Gamma参数自适应调整，从3.0变化到3.17

### 4. 方法有效性
- CA-AMA成功实现了个体理性约束
- 在保证IR的同时维持了与基线相当的收入水平
- 校正机制有效工作，IR遗憾控制在极低水平

## 技术细节

### 训练过程
1. **基线训练**: 直接优化收入，无约束
2. **CA-AMA训练**: 
   - 同时训练AMA模型和校正模型
   - 使用自适应Gamma参数控制IR约束强度
   - 校正项逐渐收敛，确保个体理性

### 测试设置
- 测试数据: 20,000个样本
- 批次大小: 5,000
- 使用相同的随机种子确保公平比较

## 结论

1. **CA-AMA方法成功实现了设计目标**：在保证个体理性的前提下实现了与基线相当的收入
2. **个体理性约束有效**：IR遗憾控制在0.0002，远低于目标阈值
3. **方法稳定性良好**：训练过程收敛，校正机制工作正常
4. **实用价值高**：为拍卖机制设计提供了理论保证的同时保持了实际性能

## 建议后续实验

1. **扩展实验**：测试更多代理和物品的组合
2. **对比实验**：与VCG等经典机制对比
3. **鲁棒性测试**：不同数据分布下的性能
4. **计算效率**：分析训练和推理时间复杂度

---
*实验完成时间: 2025-06-15*
*GPU: RTX 4070Ti*
*框架: PyTorch 2.6.0 + CUDA 12.6*
