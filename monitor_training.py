#!/usr/bin/env python3
"""
Training progress monitor for CA-AMA project
"""
import os
import time
import re

def extract_metrics_from_log(log_path):
    """Extract training metrics from log file"""
    if not os.path.exists(log_path):
        return None
    
    metrics = {
        'steps': [],
        'revenues': [],
        'payments': [],
        'ir_regrets': [],
        'ama_revs': [],
        'cor_revs': []
    }
    
    with open(log_path, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        # Extract step number and revenue (baseline)
        revenue_match = re.search(r'step (\d+): revenue: tensor\(\[([0-9.]+)\]', line)
        if revenue_match:
            step = int(revenue_match.group(1))
            revenue = float(revenue_match.group(2))
            metrics['steps'].append(step)
            metrics['revenues'].append(revenue)
        
        # Extract payment (baseline)
        payment_match = re.search(r'step (\d+): payment: ([0-9.]+)', line)
        if payment_match:
            step = int(payment_match.group(1))
            payment_str = payment_match.group(2).rstrip('.')
            payment = float(payment_str)
            if step not in metrics['steps']:
                metrics['steps'].append(step)
            metrics['payments'].append(payment)
        
        # Extract CA-AMA metrics
        caama_match = re.search(r'step (\d+): ama_rev: ([0-9.-]+), cor_rev ([0-9.-]+), ir_regret ([0-9.-]+)', line)
        if caama_match:
            step = int(caama_match.group(1))
            ama_rev = float(caama_match.group(2))
            cor_rev = float(caama_match.group(3))
            ir_regret = float(caama_match.group(4))
            if step not in metrics['steps']:
                metrics['steps'].append(step)
            metrics['ama_revs'].append(ama_rev)
            metrics['cor_revs'].append(cor_rev)
            metrics['ir_regrets'].append(ir_regret)
    
    return metrics

def monitor_training():
    """Monitor both baseline and CA-AMA training"""
    baseline_log = "results_baseline/8_2_2_32_1_1/record.log"
    caama_log = "results/8_2_2_32_1_1/record.log"
    
    print("=== Training Progress Monitor ===")
    print("Monitoring baseline and CA-AMA training...")
    print()
    
    while True:
        print(f"\n[{time.strftime('%H:%M:%S')}] Training Status:")
        print("-" * 50)
        
        # Monitor baseline
        baseline_metrics = extract_metrics_from_log(baseline_log)
        if baseline_metrics and baseline_metrics['steps']:
            latest_step = max(baseline_metrics['steps'])
            if baseline_metrics['revenues']:
                latest_revenue = baseline_metrics['revenues'][-1]
                print(f"Baseline: Step {latest_step}, Revenue: {latest_revenue:.4f}")
            elif baseline_metrics['payments']:
                latest_payment = baseline_metrics['payments'][-1]
                print(f"Baseline: Step {latest_step}, Payment: {latest_payment:.4f}")
        else:
            print("Baseline: No data yet")
        
        # Monitor CA-AMA
        caama_metrics = extract_metrics_from_log(caama_log)
        if caama_metrics and caama_metrics['steps']:
            latest_step = max(caama_metrics['steps'])
            if caama_metrics['ama_revs']:
                latest_ama = caama_metrics['ama_revs'][-1]
                latest_cor = caama_metrics['cor_revs'][-1]
                latest_ir = caama_metrics['ir_regrets'][-1]
                total_rev = latest_ama + latest_cor
                print(f"CA-AMA: Step {latest_step}, AMA: {latest_ama:.4f}, Cor: {latest_cor:.4f}, Total: {total_rev:.4f}, IR: {latest_ir:.4f}")
        else:
            print("CA-AMA: No data yet")
        
        time.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    try:
        monitor_training()
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")
