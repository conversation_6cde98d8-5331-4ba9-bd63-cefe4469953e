[2025-06-15 22:47:55,411][INFO] Namespace(n_agents=2, m_items=2, dx=10, dy=10, menu_size=32, deterministic=False, continuous_context=False, const_bidder_weights=False, d_emb=10, n_layer=3, n_head=4, d_hidden=64, init_softmax_temperature=500, alloc_softmax_temperature=10, seed=1, train_steps=1000, train_sample_num=32768, eval_freq=100, eval_sample_num=32768, batch_size=2048, device='cuda:0', lr=0.0003, decay_round_one=3000, one_lr=5e-05, decay_round_two=6000, two_lr=1e-05, load_path=None, name='./results_baseline', data=8, alpha=1, test_batch_size=1000, ablation=0)
[2025-06-15 22:47:59,997][INFO] step 5: payment: 0.4279.
[2025-06-15 22:48:02,961][INFO] step 10: payment: 0.7637.
[2025-06-15 22:48:05,941][INFO] step 15: payment: 0.7704.
[2025-06-15 22:48:09,010][INFO] step 20: payment: 0.7788.
[2025-06-15 22:48:12,004][INFO] step 25: payment: 0.7951.
[2025-06-15 22:48:14,983][INFO] step 30: payment: 0.7960.
[2025-06-15 22:48:17,928][INFO] step 35: payment: 0.8037.
[2025-06-15 22:48:20,869][INFO] step 40: payment: 0.8021.
[2025-06-15 22:48:23,832][INFO] step 45: payment: 0.7976.
[2025-06-15 22:48:26,774][INFO] step 50: payment: 0.8017.
[2025-06-15 22:48:29,750][INFO] step 55: payment: 0.8029.
[2025-06-15 22:48:32,729][INFO] step 60: payment: 0.8038.
[2025-06-15 22:48:35,667][INFO] step 65: payment: 0.7964.
[2025-06-15 22:48:38,624][INFO] step 70: payment: 0.7994.
[2025-06-15 22:48:41,601][INFO] step 75: payment: 0.8070.
[2025-06-15 22:48:44,577][INFO] step 80: payment: 0.8060.
[2025-06-15 22:48:47,522][INFO] step 85: payment: 0.8072.
[2025-06-15 22:48:50,530][INFO] step 90: payment: 0.8067.
[2025-06-15 22:48:53,508][INFO] step 95: payment: 0.8089.
[2025-06-15 22:49:01,072][INFO] step 100: revenue: tensor([0.8115], device='cuda:0')
[2025-06-15 22:49:01,876][INFO] step 100: payment: 0.8081.
[2025-06-15 22:49:04,919][INFO] step 105: payment: 0.8062.
[2025-06-15 22:49:07,938][INFO] step 110: payment: 0.8089.
[2025-06-15 22:49:10,908][INFO] step 115: payment: 0.8058.
[2025-06-15 22:49:13,895][INFO] step 120: payment: 0.8068.
[2025-06-15 22:49:16,915][INFO] step 125: payment: 0.8077.
[2025-06-15 22:49:19,982][INFO] step 130: payment: 0.8069.
[2025-06-15 22:49:22,942][INFO] step 135: payment: 0.8054.
[2025-06-15 22:49:25,920][INFO] step 140: payment: 0.8100.
[2025-06-15 22:49:28,862][INFO] step 145: payment: 0.8093.
[2025-06-15 22:49:31,803][INFO] step 150: payment: 0.8085.
[2025-06-15 22:49:34,764][INFO] step 155: payment: 0.8036.
[2025-06-15 22:49:37,724][INFO] step 160: payment: 0.8086.
[2025-06-15 22:49:40,717][INFO] step 165: payment: 0.8069.
[2025-06-15 22:49:43,756][INFO] step 170: payment: 0.8128.
[2025-06-15 22:49:46,760][INFO] step 175: payment: 0.8154.
[2025-06-15 22:49:49,738][INFO] step 180: payment: 0.8108.
[2025-06-15 22:49:52,738][INFO] step 185: payment: 0.8116.
[2025-06-15 22:49:55,823][INFO] step 190: payment: 0.8050.
[2025-06-15 22:49:58,839][INFO] step 195: payment: 0.8064.
[2025-06-15 22:50:06,156][INFO] step 200: revenue: tensor([0.8069], device='cuda:0')
[2025-06-15 22:50:06,937][INFO] step 200: payment: 0.8093.
[2025-06-15 22:50:09,948][INFO] step 205: payment: 0.8087.
[2025-06-15 22:50:12,925][INFO] step 210: payment: 0.8084.
[2025-06-15 22:50:15,878][INFO] step 215: payment: 0.8035.
[2025-06-15 22:50:18,837][INFO] step 220: payment: 0.8080.
[2025-06-15 22:50:21,804][INFO] step 225: payment: 0.8098.
[2025-06-15 22:50:24,749][INFO] step 230: payment: 0.8064.
[2025-06-15 22:50:27,726][INFO] step 235: payment: 0.8055.
[2025-06-15 22:50:30,723][INFO] step 240: payment: 0.8099.
[2025-06-15 22:50:33,799][INFO] step 245: payment: 0.8080.
[2025-06-15 22:50:36,788][INFO] step 250: payment: 0.8100.
[2025-06-15 22:50:39,741][INFO] step 255: payment: 0.8125.
[2025-06-15 22:50:42,790][INFO] step 260: payment: 0.8095.
[2025-06-15 22:50:45,801][INFO] step 265: payment: 0.8075.
[2025-06-15 22:50:48,772][INFO] step 270: payment: 0.8065.
[2025-06-15 22:50:51,765][INFO] step 275: payment: 0.8102.
[2025-06-15 22:50:54,757][INFO] step 280: payment: 0.8072.
[2025-06-15 22:50:57,774][INFO] step 285: payment: 0.8095.
[2025-06-15 22:51:00,736][INFO] step 290: payment: 0.8095.
[2025-06-15 22:51:03,754][INFO] step 295: payment: 0.8077.
[2025-06-15 22:51:11,412][INFO] step 300: revenue: tensor([0.8106], device='cuda:0')
[2025-06-15 22:51:12,135][INFO] step 300: payment: 0.8083.
[2025-06-15 22:51:15,117][INFO] step 305: payment: 0.8074.
[2025-06-15 22:51:18,087][INFO] step 310: payment: 0.8096.
[2025-06-15 22:51:21,102][INFO] step 315: payment: 0.8085.
[2025-06-15 22:51:24,121][INFO] step 320: payment: 0.8081.
[2025-06-15 22:51:27,138][INFO] step 325: payment: 0.8073.
[2025-06-15 22:51:30,121][INFO] step 330: payment: 0.8062.
[2025-06-15 22:51:33,087][INFO] step 335: payment: 0.8068.
[2025-06-15 22:51:36,078][INFO] step 340: payment: 0.8091.
[2025-06-15 22:51:39,082][INFO] step 345: payment: 0.8089.
[2025-06-15 22:51:42,061][INFO] step 350: payment: 0.8075.
[2025-06-15 22:51:45,056][INFO] step 355: payment: 0.8088.
[2025-06-15 22:51:48,040][INFO] step 360: payment: 0.8100.
[2025-06-15 22:51:51,039][INFO] step 365: payment: 0.8081.
[2025-06-15 22:51:54,065][INFO] step 370: payment: 0.8075.
[2025-06-15 22:51:57,045][INFO] step 375: payment: 0.8053.
[2025-06-15 22:51:59,986][INFO] step 380: payment: 0.8090.
[2025-06-15 22:52:02,967][INFO] step 385: payment: 0.8080.
[2025-06-15 22:52:05,954][INFO] step 390: payment: 0.8091.
[2025-06-15 22:52:08,934][INFO] step 395: payment: 0.8098.
[2025-06-15 22:52:16,503][INFO] step 400: revenue: tensor([0.8097], device='cuda:0')
[2025-06-15 22:52:17,295][INFO] step 400: payment: 0.8086.
[2025-06-15 22:52:20,272][INFO] step 405: payment: 0.8074.
[2025-06-15 22:52:23,246][INFO] step 410: payment: 0.8057.
[2025-06-15 22:52:26,214][INFO] step 415: payment: 0.8083.
[2025-06-15 22:52:29,187][INFO] step 420: payment: 0.8089.
[2025-06-15 22:52:32,180][INFO] step 425: payment: 0.8066.
[2025-06-15 22:52:35,161][INFO] step 430: payment: 0.8063.
[2025-06-15 22:52:38,125][INFO] step 435: payment: 0.8118.
[2025-06-15 22:52:41,082][INFO] step 440: payment: 0.8072.
[2025-06-15 22:52:44,055][INFO] step 445: payment: 0.8102.
[2025-06-15 22:52:47,029][INFO] step 450: payment: 0.8058.
[2025-06-15 22:52:50,024][INFO] step 455: payment: 0.8085.
[2025-06-15 22:52:52,993][INFO] step 460: payment: 0.8085.
[2025-06-15 22:52:55,990][INFO] step 465: payment: 0.8101.
[2025-06-15 22:52:58,966][INFO] step 470: payment: 0.8075.
[2025-06-15 22:53:01,968][INFO] step 475: payment: 0.8098.
[2025-06-15 22:53:04,945][INFO] step 480: payment: 0.8047.
[2025-06-15 22:53:07,965][INFO] step 485: payment: 0.8077.
[2025-06-15 22:53:10,959][INFO] step 490: payment: 0.8123.
[2025-06-15 22:53:13,941][INFO] step 495: payment: 0.8078.
[2025-06-15 22:53:21,590][INFO] step 500: revenue: tensor([0.8074], device='cuda:0')
[2025-06-15 22:53:22,434][INFO] step 500: payment: 0.8075.
[2025-06-15 22:53:25,412][INFO] step 505: payment: 0.8111.
[2025-06-15 22:53:28,383][INFO] step 510: payment: 0.8073.
[2025-06-15 22:53:31,348][INFO] step 515: payment: 0.8061.
[2025-06-15 22:53:34,336][INFO] step 520: payment: 0.8108.
[2025-06-15 22:53:37,317][INFO] step 525: payment: 0.8099.
[2025-06-15 22:53:40,301][INFO] step 530: payment: 0.8102.
[2025-06-15 22:53:43,290][INFO] step 535: payment: 0.8049.
[2025-06-15 22:53:46,261][INFO] step 540: payment: 0.8089.
[2025-06-15 22:53:49,264][INFO] step 545: payment: 0.8090.
[2025-06-15 22:53:52,271][INFO] step 550: payment: 0.8086.
[2025-06-15 22:53:55,281][INFO] step 555: payment: 0.8089.
[2025-06-15 22:53:58,290][INFO] step 560: payment: 0.8049.
[2025-06-15 22:54:01,294][INFO] step 565: payment: 0.8082.
[2025-06-15 22:54:04,470][INFO] step 570: payment: 0.8096.
[2025-06-15 22:54:07,506][INFO] step 575: payment: 0.8123.
[2025-06-15 22:54:10,532][INFO] step 580: payment: 0.8071.
[2025-06-15 22:54:13,523][INFO] step 585: payment: 0.8061.
[2025-06-15 22:54:16,513][INFO] step 590: payment: 0.8079.
[2025-06-15 22:54:19,504][INFO] step 595: payment: 0.8097.
[2025-06-15 22:54:26,422][INFO] step 600: revenue: tensor([0.8060], device='cuda:0')
[2025-06-15 22:54:27,227][INFO] step 600: payment: 0.8093.
[2025-06-15 22:54:30,245][INFO] step 605: payment: 0.8098.
[2025-06-15 22:54:33,274][INFO] step 610: payment: 0.8071.
[2025-06-15 22:54:36,289][INFO] step 615: payment: 0.8104.
[2025-06-15 22:54:39,297][INFO] step 620: payment: 0.8092.
[2025-06-15 22:54:42,311][INFO] step 625: payment: 0.8096.
[2025-06-15 22:54:45,332][INFO] step 630: payment: 0.8062.
[2025-06-15 22:54:48,303][INFO] step 635: payment: 0.8063.
[2025-06-15 22:54:51,295][INFO] step 640: payment: 0.8086.
[2025-06-15 22:54:54,276][INFO] step 645: payment: 0.8114.
[2025-06-15 22:54:57,341][INFO] step 650: payment: 0.8072.
[2025-06-15 22:55:00,294][INFO] step 655: payment: 0.8095.
[2025-06-15 22:55:03,272][INFO] step 660: payment: 0.8083.
[2025-06-15 22:55:06,250][INFO] step 665: payment: 0.8110.
[2025-06-15 22:55:09,255][INFO] step 670: payment: 0.8152.
[2025-06-15 22:55:15,451][INFO] step 675: payment: 0.8077.
[2025-06-15 22:55:22,080][INFO] step 680: payment: 0.8097.
[2025-06-15 22:55:28,981][INFO] step 685: payment: 0.8101.
[2025-06-15 22:55:35,888][INFO] step 690: payment: 0.8113.
[2025-06-15 22:55:42,727][INFO] step 695: payment: 0.8065.
[2025-06-15 22:55:53,378][INFO] step 700: revenue: tensor([0.8108], device='cuda:0')
[2025-06-15 22:55:54,751][INFO] step 700: payment: 0.8104.
[2025-06-15 22:56:01,636][INFO] step 705: payment: 0.8083.
[2025-06-15 22:56:08,517][INFO] step 710: payment: 0.8092.
[2025-06-15 22:56:15,341][INFO] step 715: payment: 0.8090.
[2025-06-15 22:56:22,186][INFO] step 720: payment: 0.8078.
[2025-06-15 22:56:29,005][INFO] step 725: payment: 0.8099.
[2025-06-15 22:56:35,862][INFO] step 730: payment: 0.8100.
[2025-06-15 22:56:42,730][INFO] step 735: payment: 0.8113.
[2025-06-15 22:56:49,531][INFO] step 740: payment: 0.8117.
[2025-06-15 22:56:56,351][INFO] step 745: payment: 0.8095.
[2025-06-15 22:57:03,070][INFO] step 750: payment: 0.8079.
[2025-06-15 22:57:09,879][INFO] step 755: payment: 0.8095.
[2025-06-15 22:57:16,667][INFO] step 760: payment: 0.8098.
[2025-06-15 22:57:23,480][INFO] step 765: payment: 0.8105.
[2025-06-15 22:57:29,572][INFO] step 770: payment: 0.8086.
[2025-06-15 22:57:33,040][INFO] step 775: payment: 0.8098.
[2025-06-15 22:57:38,583][INFO] step 780: payment: 0.8107.
[2025-06-15 22:57:45,407][INFO] step 785: payment: 0.8060.
[2025-06-15 22:57:52,230][INFO] step 790: payment: 0.8073.
[2025-06-15 22:57:59,085][INFO] step 795: payment: 0.8086.
[2025-06-15 22:58:10,617][INFO] step 800: revenue: tensor([0.8106], device='cuda:0')
[2025-06-15 22:58:11,987][INFO] step 800: payment: 0.8087.
[2025-06-15 22:58:18,765][INFO] step 805: payment: 0.8120.
[2025-06-15 22:58:25,578][INFO] step 810: payment: 0.8087.
[2025-06-15 22:58:32,380][INFO] step 815: payment: 0.8118.
[2025-06-15 22:58:39,200][INFO] step 820: payment: 0.8085.
[2025-06-15 22:58:45,998][INFO] step 825: payment: 0.8085.
[2025-06-15 22:58:52,787][INFO] step 830: payment: 0.8119.
[2025-06-15 22:58:59,600][INFO] step 835: payment: 0.8075.
[2025-06-15 22:59:06,422][INFO] step 840: payment: 0.8068.
[2025-06-15 22:59:13,222][INFO] step 845: payment: 0.8077.
[2025-06-15 22:59:20,020][INFO] step 850: payment: 0.8101.
[2025-06-15 22:59:26,840][INFO] step 855: payment: 0.8074.
[2025-06-15 22:59:33,702][INFO] step 860: payment: 0.8072.
[2025-06-15 22:59:40,606][INFO] step 865: payment: 0.8059.
[2025-06-15 22:59:47,482][INFO] step 870: payment: 0.8121.
[2025-06-15 22:59:54,300][INFO] step 875: payment: 0.8078.
[2025-06-15 22:59:58,143][INFO] step 880: payment: 0.8077.
[2025-06-15 23:00:02,452][INFO] step 885: payment: 0.8059.
[2025-06-15 23:00:09,273][INFO] step 890: payment: 0.8066.
[2025-06-15 23:00:16,100][INFO] step 895: payment: 0.8101.
[2025-06-15 23:00:27,527][INFO] step 900: revenue: tensor([0.8091], device='cuda:0')
[2025-06-15 23:00:28,892][INFO] step 900: payment: 0.8085.
[2025-06-15 23:00:35,739][INFO] step 905: payment: 0.8086.
[2025-06-15 23:00:42,555][INFO] step 910: payment: 0.8080.
[2025-06-15 23:00:49,416][INFO] step 915: payment: 0.8086.
[2025-06-15 23:00:56,232][INFO] step 920: payment: 0.8089.
[2025-06-15 23:01:02,943][INFO] step 925: payment: 0.8080.
[2025-06-15 23:01:09,749][INFO] step 930: payment: 0.8063.
[2025-06-15 23:01:16,557][INFO] step 935: payment: 0.8036.
[2025-06-15 23:01:23,445][INFO] step 940: payment: 0.8080.
[2025-06-15 23:01:30,406][INFO] step 945: payment: 0.8104.
[2025-06-15 23:01:37,314][INFO] step 950: payment: 0.8127.
[2025-06-15 23:01:44,220][INFO] step 955: payment: 0.8088.
[2025-06-15 23:01:51,070][INFO] step 960: payment: 0.8088.
[2025-06-15 23:01:57,902][INFO] step 965: payment: 0.8105.
[2025-06-15 23:02:04,677][INFO] step 970: payment: 0.8123.
[2025-06-15 23:02:11,464][INFO] step 975: payment: 0.8097.
[2025-06-15 23:02:18,302][INFO] step 980: payment: 0.8065.
[2025-06-15 23:02:23,493][INFO] step 985: payment: 0.8127.
[2025-06-15 23:02:27,172][INFO] step 990: payment: 0.8099.
[2025-06-15 23:02:33,540][INFO] step 995: payment: 0.8080.
[2025-06-15 23:02:44,630][INFO] step 1000: revenue: tensor([0.8067], device='cuda:0')
[2025-06-15 23:02:46,010][INFO] step 1000: payment: 0.8091.
[2025-06-15 23:02:46,011][INFO] ------------Final test------------
