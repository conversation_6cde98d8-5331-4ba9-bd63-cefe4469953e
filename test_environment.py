#!/usr/bin/env python3
"""
Environment test script for CA-AMA project
"""
import torch
import numpy as np
from tqdm import tqdm
import os

def test_environment():
    print("=== Environment Test ===")
    
    # Test PyTorch and CUDA
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        print(f"Current GPU: {torch.cuda.current_device()}")
        print(f"GPU name: {torch.cuda.get_device_name(0)}")
    
    # Test basic tensor operations on GPU
    if torch.cuda.is_available():
        device = torch.device("cuda:0")
        x = torch.randn(1000, 1000).to(device)
        y = torch.randn(1000, 1000).to(device)
        z = torch.mm(x, y)
        print(f"GPU tensor operation successful: {z.shape}")
    
    # Test data files
    print("\n=== Data Files Check ===")
    data_files = ['./data/data_8_means.npy', './data/data_8_covs.npy']
    for file in data_files:
        if os.path.exists(file):
            data = np.load(file)
            print(f"✓ {file}: shape {data.shape}")
        else:
            print(f"✗ {file}: NOT FOUND")
    
    # Test imports
    print("\n=== Module Imports Test ===")
    try:
        from auction import ContextualAffineMaximizerAuction
        print("✓ auction module imported successfully")
    except ImportError as e:
        print(f"✗ auction module import failed: {e}")
    
    try:
        from net import Payment_Cor
        print("✓ net module imported successfully")
    except ImportError as e:
        print(f"✗ net module import failed: {e}")
    
    try:
        from gen_values import generate_data_8
        print("✓ gen_values module imported successfully")
    except ImportError as e:
        print(f"✗ gen_values module import failed: {e}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_environment()
