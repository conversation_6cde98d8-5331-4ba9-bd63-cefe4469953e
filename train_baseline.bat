@echo off
REM ##########
REM ## Normal Distribution:
REM ## --data 8

set device=cuda:0
for %%s in (1 2 3 4 5) do (
    python train_baseline.py --menu_size 32 --n_agents 2 --m_items 2 --device %device% --name "./results_baseline" --const_bidder_weights False --data 8 --seed %%s
    python train_baseline.py --menu_size 256 --n_agents 10 --m_items 2 --device %device% --name "./results_baseline" --const_bidder_weights False --data 8 --seed %%s
)

echo Training completed!
pause
